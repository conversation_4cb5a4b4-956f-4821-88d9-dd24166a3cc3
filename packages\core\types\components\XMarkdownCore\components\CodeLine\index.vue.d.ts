import { CodeLineProps } from './types';
declare const _default: import('vue').DefineComponent<CodeLineProps, {}, {}, {}, {}, import('vue').ComponentOptionsMixin, import('vue').ComponentOptionsMixin, {}, string, import('vue').PublicProps, Readonly<CodeLineProps> & Readonly<{}>, {
    content: string;
    raw: {
        content?: string;
        inline?: boolean;
    };
}, {}, {}, {}, string, import('vue').ComponentProvideOptions, false, {}, HTMLSpanElement>;
export default _default;
