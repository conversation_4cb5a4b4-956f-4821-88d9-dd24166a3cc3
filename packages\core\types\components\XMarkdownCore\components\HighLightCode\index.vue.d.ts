export interface HighLightCodeProps {
    code: string[];
    lang: string;
    enableCodeLineNumber: boolean;
}
declare const _default: import('vue').DefineComponent<HighLightCodeProps, {}, {}, {}, {}, import('vue').ComponentOptionsMixin, import('vue').ComponentOptionsMixin, {}, string, import('vue').PublicProps, Readonly<HighLightCodeProps> & Readonly<{}>, {}, {}, {}, {}, string, import('vue').ComponentProvideOptions, false, {}, HTMLDivElement>;
export default _default;
