import { ElxRunCodeContentProps } from '../type';
declare const _default: import('vue').DefineComponent<ElxRunCodeContentProps, {}, {}, {}, {}, import('vue').ComponentOptionsMixin, import('vue').ComponentOptionsMixin, {}, string, import('vue').PublicProps, Readonly<ElxRunCodeContentProps> & Readonly<{}>, {}, {}, {}, {}, string, import('vue').ComponentProvideOptions, false, {
    codeContainerRef: unknown;
    iframeRef: HTMLIFrameElement;
}, any>;
export default _default;
