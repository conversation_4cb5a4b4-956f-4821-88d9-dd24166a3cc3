{"name": "docs", "type": "module", "version": "0.0.0", "private": true, "scripts": {"dev": "vitepress dev --port 5174", "build": "vitepress build", "preview": "vitepress preview"}, "dependencies": {"echarts": "^5.6.0", "element-plus-x-metadata": "workspace:^", "gsap": "^3.13.0", "vitepress-plugin-group-icons": "^1.6.1", "vue": "^3.5.17", "vue-element-plus-x": "workspace:^"}, "devDependencies": {"@ant-design/colors": "^8.0.0", "@vitejs/plugin-vue-jsx": "^4.2.0", "less": "^4.3.0", "unocss": "66.1.0-beta.12", "unplugin-element-plus": "^0.10.0", "vite": "^6.3.5", "vite-plugin-eslint": "^1.8.1", "vite-plugin-prismjs": "^0.0.11", "vite-plugin-tsx-auto-props": "^0.0.8", "vite-plugin-vitepress-demo": "^2.2.1", "vitepress": "^1.6.3", "vitepress-demo-plugin": "^1.4.5"}}