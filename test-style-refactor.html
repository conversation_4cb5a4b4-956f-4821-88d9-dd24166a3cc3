<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>样式重构测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }
        
        .test-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            min-width: 300px;
        }
        
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        
        /* 自定义类名样式测试 */
        .custom-list {
            background-color: #f0f8ff !important;
            border: 2px solid #4a90e2 !important;
        }
        
        .custom-item {
            background-color: #e8f4fd !important;
            border-left: 4px solid #4a90e2 !important;
        }
        
        .custom-item-hover {
            background-color: #d1e7dd !important;
            transform: translateX(5px) !important;
            transition: all 0.3s ease !important;
        }
        
        .custom-item-active {
            background-color: #0d6efd !important;
            color: white !important;
            font-weight: bold !important;
        }
        
        .custom-item-menu-opened {
            background-color: #fff3cd !important;
            border-color: #ffc107 !important;
        }
        
        .status {
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            margin: 5px 0;
        }
        
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .feature-list li:before {
            content: "✓";
            color: #28a745;
            font-weight: bold;
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <h1>Conversations 组件样式重构测试</h1>
    
    <div class="test-container">
        <div class="test-section">
            <div class="test-title">重构完成功能</div>
            <ul class="feature-list">
                <li>主组件内联样式改为类名形式</li>
                <li>Item组件内联样式改为类名形式</li>
                <li>支持自定义CSS类名</li>
                <li>保持向后兼容性</li>
                <li>TypeScript类型定义更新</li>
            </ul>
            
            <div class="status success">
                ✅ 重构完成：内联样式已成功转换为类名形式
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">新增类名属性</div>
            <div class="status info">容器类名属性：</div>
            <ul>
                <li><code>className</code> - 容器自定义类名</li>
            </ul>
            
            <div class="status info">Item类名属性：</div>
            <ul>
                <li><code>itemsClassName</code> - 基础类名</li>
                <li><code>itemsHoverClassName</code> - 悬停类名</li>
                <li><code>itemsActiveClassName</code> - 激活类名</li>
                <li><code>itemsMenuOpenedClassName</code> - 菜单打开类名</li>
            </ul>
        </div>
        
        <div class="test-section">
            <div class="test-title">使用示例</div>
            <pre><code>&lt;Conversations
  :items="items"
  className="custom-list"
  itemsClassName="custom-item"
  itemsHoverClassName="custom-item-hover"
  itemsActiveClassName="custom-item-active"
  itemsMenuOpenedClassName="custom-item-menu-opened"
  @change="handleChange"
/&gt;</code></pre>
            
            <div class="status success">
                💡 现在可以使用CSS类名代替内联样式，提供更好的样式管理和性能
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">兼容性说明</div>
            <div class="status info">
                原有的样式属性仍然支持：
            </div>
            <ul>
                <li><code>style</code> - 容器样式</li>
                <li><code>itemsStyle</code> - Item基础样式</li>
                <li><code>itemsHoverStyle</code> - Item悬停样式</li>
                <li><code>itemsActiveStyle</code> - Item激活样式</li>
                <li><code>itemsMenuOpenedStyle</code> - Item菜单打开样式</li>
            </ul>
            
            <div class="status success">
                ✅ 完全向后兼容，现有代码无需修改
            </div>
        </div>
    </div>
    
    <div style="margin-top: 30px; padding: 20px; background: #fff; border-radius: 8px;">
        <h2>测试结果总结</h2>
        <div class="status success">
            🎉 样式重构成功完成！组件现在支持使用CSS类名进行样式定制，同时保持完全的向后兼容性。
        </div>
    </div>
</body>
</html>
