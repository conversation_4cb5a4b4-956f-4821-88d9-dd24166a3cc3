import { BubbleProps } from '../components/Bubble/types';
import { BubbleListProps } from '../components/BubbleList/types';
import { FilesType } from '../components/FilesCard/types';
import { ThinkingStatus } from '../components/Thinking/types';
export declare const avatar1: string;
export declare const avatar2: string;
export declare const mdContent: string;
export declare const highlightMdContent: string;
export declare const mermaidMdContent = "\n\n### mermaid \u997C\u72B6\u56FE\n```mermaid\npie\n    \"\u4F20\u5A92\u53CA\u6587\u5316\u76F8\u5173\" : 35\n    \"\u5E7F\u544A\u4E0E\u5E02\u573A\u8425\u9500\" : 8\n    \"\u6E38\u620F\u5F00\u53D1\" : 15\n    \"\u5F71\u89C6\u52A8\u753B\u4E0E\u7279\u6548\" : 12\n    \"\u4E92\u8054\u7F51\u4EA7\u54C1\u8BBE\u8BA1\" : 10\n    \"VR/AR\u5F00\u53D1\" : 5\n    \"\u5176\u4ED6\" : 15\n```\n\n";
export declare const mathMdContent = "\n### mermaid \u6D41\u7A0B\u56FE\n```mermaid\ngraph LR\n    1 --> 2\n    2 --> 3\n    3 --> 4\n    2 --> 1\n    2-3 --> 1-3\n```\n```mermaid\nflowchart TD\n    Start[\u5F00\u59CB] --> Check[\u662F\u5426\u901A\u8FC7\uFF1F]\n    Check -- \u662F --> Pass[\u6D41\u7A0B\u7EE7\u7EED]\n    Check -- \u5426 --> Reject[\u6D41\u7A0B\u7ED3\u675F]\n```\n```mermaid\nflowchart TD\n    %% \u524D\u7AEF\u4E13\u9879\u56DB\u5C42\u7ED3\u6784\n    A[\"\u6218\u7565\u5C42\n    \u3010\u63D0\u5347\u7528\u6237\u4F53\u9A8C\u3011\"]\n    --> B[\"\u67B6\u6784\u5C42\n    \u3010\u5FAE\u524D\u7AEF\u65B9\u6848\u9009\u578B\u3011\"]\n    --> C[\"\u6846\u67B6\u5C42\n    \u3010React+TS\u6280\u672F\u6808\u3011\"]\n    --> D[\"\u5B9E\u73B0\u5C42\n    \u3010\u7EC4\u4EF6\u5E93\u5F00\u53D1\u3011\"]\n    style A fill:#FFD700,stroke:#FFA500\n    style B fill:#87CEFA,stroke:#1E90FF\n    style C fill:#9370DB,stroke:#663399\n    style D fill:#FF6347,stroke:#CD5C5C\n\n```\n### mermaid \u6570\u5B66\u516C\u5F0F\n```mermaid\nsequenceDiagram\n    autonumber\n    participant 1 as $$alpha$$\n    participant 2 as $$beta$$\n    1->>2: Solve: $$sqrt{2+2}$$\n    2-->>1: Answer: $$2$$\n```\n\n";
export declare const customAttrContent = "\n<a href=\"https://element-plus-x.com/\">element-plus-x</a>\n<h1>\u6807\u98981</h1>\n<h2>\u6807\u98982</h2>\n";
export type MessageItem = BubbleProps & {
    key: number;
    role: 'ai' | 'user' | 'system';
    avatar: string;
    thinkingStatus?: ThinkingStatus;
    expanded?: boolean;
};
export declare const mermaidComplexMdContent = "\n### Mermaid \u6E32\u67D3\u590D\u6742\u56FE\u8868\u6848\u4F8B\n```mermaid\ngraph LR\n    A[\u7528\u6237] -->|\u8BF7\u6C42\u4EA4\u4E92| B[\u524D\u7AEF\u5E94\u7528]\n    B -->|API\u8C03\u7528| C[API\u7F51\u5173]\n    C -->|\u8BA4\u8BC1\u8BF7\u6C42| D[\u8BA4\u8BC1\u670D\u52A1]\n    C -->|\u4E1A\u52A1\u8BF7\u6C42| E[\u4E1A\u52A1\u670D\u52A1]\n    E -->|\u6570\u636E\u8BFB\u5199| F[\u6570\u636E\u5E93]\n    E -->|\u7F13\u5B58\u64CD\u4F5C| G[\u7F13\u5B58\u670D\u52A1]\n    E -->|\u6D88\u606F\u53D1\u5E03| H[\u6D88\u606F\u961F\u5217]\n    H -->|\u89E6\u53D1\u4EFB\u52A1| I[\u540E\u53F0\u4EFB\u52A1]\n\n    subgraph \"\u5FAE\u670D\u52A1\u96C6\u7FA4\"\n        D[\u8BA4\u8BC1\u670D\u52A1]\n        E[\u4E1A\u52A1\u670D\u52A1]\n        I[\u540E\u53F0\u4EFB\u52A1]\n    end\n\n    subgraph \"\u6570\u636E\u6301\u4E45\u5C42\"\n        F[\u6570\u636E\u5E93]\n        G[\u7F13\u5B58\u670D\u52A1]\n    end\n\n";
export declare const messageArr: BubbleListProps<MessageItem>['list'];
export declare const colorMap: Record<FilesType, string>;
export declare const colorMap1: Record<FilesType, string>;
export declare const colorMap2: Record<FilesType, string>;
