import { CodeBlockExpose } from '../../components/XMarkdownCore/components/CodeBlock/shiki-header';
declare const _default: import('vue').DefineComponent<CodeBlockExpose, {}, {}, {}, {}, import('vue').ComponentOptionsMixin, import('vue').ComponentOptionsMixin, {}, string, import('vue').PublicProps, Readonly<CodeBlockExpose> & Readonly<{}>, {}, {}, {}, {}, string, import('vue').ComponentProvideOptions, false, {}, HTMLDivElement>;
export default _default;
