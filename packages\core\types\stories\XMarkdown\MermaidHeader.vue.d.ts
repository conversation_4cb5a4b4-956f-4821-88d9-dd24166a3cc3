import { MermaidExposeProps } from '../../components/XMarkdownCore/components/Mermaid/types';
declare const _default: import('vue').DefineComponent<MermaidExposeProps, {}, {}, {}, {}, import('vue').ComponentOptionsMixin, import('vue').ComponentOptionsMixin, {}, string, import('vue').PublicProps, Readonly<MermaidExposeProps> & Readonly<{}>, {}, {}, {}, {}, string, import('vue').ComponentProvideOptions, false, {}, HTMLDivElement>;
export default _default;
