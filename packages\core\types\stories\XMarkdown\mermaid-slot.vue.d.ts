import { MermaidToolbarConfig } from '../../components/XMarkdownCore/components/Mermaid/types';
type __VLS_Props = {
    markdown: string;
    mermaidConfig?: MermaidToolbarConfig;
    themes?: {
        light: string;
        dark: string;
    };
};
declare const _default: import('vue').DefineComponent<__VLS_Props, {}, {}, {}, {}, import('vue').ComponentOptionsMixin, import('vue').ComponentOptionsMixin, {}, string, import('vue').PublicProps, Readonly<__VLS_Props> & Readonly<{}>, {}, {}, {}, {}, string, import('vue').ComponentProvideOptions, false, {}, any>;
export default _default;
