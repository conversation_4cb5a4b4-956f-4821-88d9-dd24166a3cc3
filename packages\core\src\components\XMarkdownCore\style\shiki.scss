// // 媒体查询：当用户系统主题为暗色时 基于浏览器的主题色
// @media (prefers-color-scheme: light) {
//   body {
//     --shiki-custom-brr-mini: 3px;
//     --shiki-custom-brr: 5px;
//     --shiki-custom-blur: 10px;
//     --shiki-code-header-bg: #fafafa;
//     --shiki-code-header-span-color: #575757;
//   }
// }

// // 媒体查询：当用户系统主题为暗色时
// @media (prefers-color-scheme: dark) {
//   body {
//     --shiki-custom-brr-mini: 3px;
//     --shiki-custom-brr: 5px;
//     --shiki-custom-blur: 10px;
//     --shiki-code-header-bg: #272727;
//     --shiki-code-header-span-color: #fafafa;
//   }

//   .shiki,
//   .shiki span {
//     color: var(--shiki-dark) !important;
//     background-color: var(--shiki-dark-bg) !important;
//     /* 可选，用于定义字体样式 */
//     font-style: var(--shiki-dark-font-style) !important;
//     font-weight: var(--shiki-dark-font-weight) !important;
//     text-decoration: var(--shiki-dark-text-decoration) !important;
//   }
// }

body {
  --shiki-custom-brr-mini: 3px;
  --shiki-custom-brr: 5px;
  --shiki-custom-blur: 10px;
  --shiki-code-header-bg: #fafafa;
  --shiki-code-header-span-color: #575757;
  --shiki-code-header-btn-bg: #ebedf0;
}

body.dark {
  --shiki-custom-brr-mini: 3px;
  --shiki-custom-brr: 5px;
  --shiki-custom-blur: 10px;
  --shiki-code-header-bg: #272727;
  --shiki-code-header-span-color: #fafafa;
  --shiki-code-header-btn-bg: #3b3b3b;
}

body.dark .shiki,
body.dark .shiki span {
  color: var(--shiki-dark) !important;
  background-color: var(--shiki-dark-bg) !important;
  /* 可选，用于定义字体样式 */
  font-style: var(--shiki-dark-font-style) !important;
  font-weight: var(--shiki-dark-font-weight) !important;
  text-decoration: var(--shiki-dark-text-decoration) !important;
}

.shiki {
  position: relative;
}

.elx-xmarkdown-container {
  // background-color: var(--el-fill-color);
  color: black;
  padding: 5px 10px;
  border-radius: var(--shiki-custom-brr);

  /* 表格整体边框 */
  table {
    border-collapse: collapse;
    width: 100%;
    border: 1px solid #ddd;
  }

  /* 表头样式 */
  th {
    background-color: white;
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
  }

  /* 表格行样式 */
  td {
    border: 1px solid #ddd;
    padding: 8px;
  }

  tbody {
    tr:nth-child(even) {
      background-color: white;
    }

    tr:nth-child(odd) {
      background-color: var(--el-fill-color-lighter);
    }
  }

  pre code {
    text-shadow: none !important;
    // 设置代码字体样式
    span {
      font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
      font-size: 1rem;
      line-height: 1.5rem;
      text-align: left;
      // white-space: pre-wrap;
      word-spacing: normal;
      word-break: normal;
      word-wrap: normal;
      tab-size: 4;
      -webkit-hyphens: none;
      -moz-hyphens: none;
      -ms-hyphens: none;
      hyphens: none;
    }
  }

  pre div.pre-md {
    position: relative;
    border-radius: var(--shiki-custom-brr-mini);
    border: 1px solid var(--el-border-color);
    min-width: 180px !important;
    .markdown-elxLanguage-header-div {
      box-sizing: content-box !important;
      position: sticky;
      top: 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      padding: 5px 5px 5px 8px;
      border-radius: var(--shiki-custom-brr-mini);
      -webkit-backdrop-filter: blur(var(--shiki-custom-blur));
      backdrop-filter: blur(var(--shiki-custom-blur));
      margin: 0;
      background-color: var(--shiki-code-header-bg);
      z-index: 1;
      span {
        box-shadow: none !important;
        background-color: transparent !important;
      }

      .markdown-elxLanguage-header-span {
        font-size: 14px;
        font-family: sans-serif;
        color: var(--shiki-code-header-span-color) !important;
        font-weight: bold !important;
        background-color: transparent !important;
        -webkit-user-select: none;
        user-select: none;
        &:hover {
          cursor: pointer !important;
        }
      }

      .markdown-elxLanguage-header-space-start,
      .markdown-elxLanguage-header-space {
        display: flex;
        justify-content: flex-end;
        align-items: center;
      }

      .markdown-elxLanguage-header-space-start {
        justify-content: flex-start;
      }

      .shiki-header-button {
        border: 1px solid transparent;
        border-radius: var(--shiki-custom-brr);
        background-color: transparent;
        width: fit-content !important;
        padding: 0px 3px;
        height: 24px;
        opacity: 1;
        transition: color 0.3s ease-in-out;
        cursor: pointer;
        .el-icon {
          font-size: 15px !important;
        }
        svg {
          width: 15px;
          height: 15px;
        }
        .el-icon,
        span {
          background-color: transparent !important;
          color: var(--shiki-code-header-span-color) !important;
        }
        &:hover {
          background-color: var(--shiki-code-header-btn-bg);
        }
      }
      // 按钮图标的大小
      .shiki-header-button {
        span {
          width: 16px;
          height: 16px;
        }
      }
      .shiki-header-button-expand {
        span {
          width: 12px;
          height: 12px;
        }
      }

      .markdown-elxLanguage-header-toggle-expand {
        margin: 2px 0 0 0;
        transition: transform 0.3s ease-in-out;
        transform: rotate(-90deg);
      }

      .copied {
        color: var(--el-color-success);
      }
    }

    code {
      flex: 1;
      box-sizing: border-box;
      width: 100%;
      height: 0;
    }
  }

  pre div.is-expanded {
    height: auto !important;
    code {
      padding: 8px !important;
      height: auto !important;
    }
    .markdown-elxLanguage-header-toggle-expand {
      transform: rotate(0deg) !important;
    }
  }
}
