export type SSEFields = 'data' | 'event' | 'id' | 'retry';
export type SSEOutput = Partial<Record<SSEFields, any>>;
export interface XStreamOptions<Output = SSEOutput> {
    readableStream: ReadableStream<Uint8Array>;
    transformStream?: TransformStream<string, Output>;
}
export declare function useXStream(): {
    startStream: (options: XStreamOptions<SSEOutput>) => Promise<void>;
    cancel: () => void;
    data: any;
    error: any;
    isLoading: any;
};
