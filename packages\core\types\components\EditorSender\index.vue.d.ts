import { <PERSON>t<PERSON><PERSON>ate<PERSON><PERSON>, TagInfo, TipOptions, UserInfo, default as Cha<PERSON><PERSON><PERSON> } from 'chatarea';
import { EditorProps, MixTag, SelectDialogOption, SubmitResult } from './types';
declare function getCurrentValue(): SubmitResult;
declare function onClear(txt?: string): void;
declare function focusToStart(): void;
declare function focusToEnd(): void;
declare function blur(): void;
declare function selectAll(): void;
declare function setSelectTag(key: string, tagId: string): void;
declare function setInputTag(key: string, placeholder: string, defaultValue?: string): void;
declare function setUserTag(userId: string): void;
declare function setCustomTag(prefix: string, id: string): void;
declare function setMixTags(tags: MixTag[][]): void;
declare function setHtml(html: string): void;
declare function setText(txt: string): void;
declare function openSelectDialog(option: SelectDialogOption): void;
declare function openTipTag(options: TipOptions): void;
declare function closeTipTag(): void;
declare function customSetUser(user: UserInfo): void;
declare function customSetTag(prefix: string, tag: TagInfo): void;
declare function updateSelectTag(elm: HTMLElement, tag: TagInfo): void;
declare function __VLS_template(): {
    attrs: Partial<{}>;
    slots: {
        header?(_: {}): any;
        prefix?(_: {}): any;
        prefix?(_: {}): any;
        'action-list'?(_: {}): any;
        'action-list'?(_: {}): any;
        footer?(_: {}): any;
    };
    refs: {
        container: HTMLDivElement;
    };
    rootEl: HTMLDivElement;
};
type __VLS_TemplateResult = ReturnType<typeof __VLS_template>;
declare const __VLS_component: import('vue').DefineComponent<EditorProps, {
    getCurrentValue: typeof getCurrentValue;
    focusToStart: typeof focusToStart;
    focusToEnd: typeof focusToEnd;
    blur: typeof blur;
    selectAll: typeof selectAll;
    clear: typeof onClear;
    setSelectTag: typeof setSelectTag;
    setInputTag: typeof setInputTag;
    setUserTag: typeof setUserTag;
    setCustomTag: typeof setCustomTag;
    setMixTags: typeof setMixTags;
    setHtml: typeof setHtml;
    setText: typeof setText;
    openSelectDialog: typeof openSelectDialog;
    customSetUser: typeof customSetUser;
    customSetTag: typeof customSetTag;
    updateSelectTag: typeof updateSelectTag;
    openTipTag: typeof openTipTag;
    closeTipTag: typeof closeTipTag;
    chat: import('vue').Ref<ChatArea<UserInfo> | undefined, ChatArea<UserInfo> | undefined>;
    opNode: import('vue').Ref<ChatOperateNode | undefined, ChatOperateNode | undefined>;
    chatState: {
        isEmpty: boolean;
        textLength: number;
        lastFocusNode: Node | null;
        lastOffset: number;
        wrapCallSelectDialog: boolean;
        beforeText?: string | undefined;
        afterText?: string | undefined;
    };
}, {}, {}, {}, import('vue').ComponentOptionsMixin, import('vue').ComponentOptionsMixin, {} & {
    cancel: () => any;
    change: () => any;
    submit: (payload: SubmitResult) => any;
    showAtDialog: () => any;
    showTagDialog: (prefix: string) => any;
    showSelectDialog: (key: string, elm: HTMLElement) => any;
}, string, import('vue').PublicProps, Readonly<EditorProps> & Readonly<{
    onCancel?: (() => any) | undefined;
    onChange?: (() => any) | undefined;
    onSubmit?: ((payload: SubmitResult) => any) | undefined;
    onShowAtDialog?: (() => any) | undefined;
    onShowTagDialog?: ((prefix: string) => any) | undefined;
    onShowSelectDialog?: ((key: string, elm: HTMLElement) => any) | undefined;
}>, {
    disabled: boolean;
    placeholder: string;
    loading: boolean;
    variant: "default" | "updown";
    device: "pc" | "h5";
    autoFocus: boolean;
    userList: import('./types').UserInfo[];
    customTrigger: import('./types').CustomTag[];
    selectList: import('./types').SelectTag[];
    maxLength: number;
    submitType: "enter" | "shiftEnter";
    customStyle: Record<string, any>;
    clearable: boolean;
    headerAnimationTimer: number;
    asyncMatchFun: (searchVal: string) => Promise<import('./types').UserInfo[]>;
    customDialog: boolean;
}, {}, {}, {}, string, import('vue').ComponentProvideOptions, false, {
    container: HTMLDivElement;
}, HTMLDivElement>;
declare const _default: __VLS_WithTemplateSlots<typeof __VLS_component, __VLS_TemplateResult["slots"]>;
export default _default;
type __VLS_WithTemplateSlots<T, S> = T & {
    new (): {
        $slots: S;
    };
};
