import { AttachmentsProps } from '../../components/Attachments/types';
type Props = Pick<AttachmentsProps, 'items'>;
declare const _default: import('vue').DefineComponent<Props, {}, {}, {}, {}, import('vue').ComponentOptionsMixin, import('vue').ComponentOptionsMixin, {}, string, import('vue').PublicProps, Readonly<Props> & Readonly<{}>, {
    items: import('../../components/FilesCard/types').FilesCardProps[];
}, {}, {}, {}, string, import('vue').ComponentProvideOptions, false, {}, HTMLDivElement>;
export default _default;
