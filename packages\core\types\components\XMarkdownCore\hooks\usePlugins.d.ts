import { Pluggable } from 'unified';
import { default as remarkGfm } from 'remark-gfm';
declare function usePlugins(props: any): {
    rehypePlugins: import('vue').ComputedRef<Pluggable[]>;
    remarkPlugins: import('vue').ComputedRef<(import('unified').Plugin<any[], any, any> | [plugin: import('unified').Plugin<any[], any, any>, ...parameters: any[]] | import('unified').Preset | {
        plugins: Pluggable[];
    } | (typeof remarkGfm | {
        singleTilde: boolean;
    })[])[]>;
};
export { usePlugins };
