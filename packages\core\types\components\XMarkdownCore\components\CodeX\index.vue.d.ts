import { h } from 'vue';
declare const _default: import('vue').DefineComponent<import('vue').ExtractPropTypes<{
    raw: {
        type: ObjectConstructor;
        default: () => {};
    };
}>, () => ReturnType<typeof h> | null, {}, {}, {}, import('vue').ComponentOptionsMixin, import('vue').ComponentOptionsMixin, {}, string, import('vue').PublicProps, Readonly<import('vue').ExtractPropTypes<{
    raw: {
        type: ObjectConstructor;
        default: () => {};
    };
}>> & Readonly<{}>, {
    raw: Record<string, any>;
}, {}, {}, {}, string, import('vue').ComponentProvideOptions, true, {}, any>;
export default _default;
