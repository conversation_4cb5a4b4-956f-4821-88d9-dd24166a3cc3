import { GlobalShiki } from '../../hooks/useShiki';
import { Ref } from 'vue';
import { MarkdownContext } from './types';
declare const MarkdownProvider: import('vue').DefineComponent<import('vue').ExtractPropTypes<{
    markdown: {
        type: StringConstructor;
        default: string;
    };
    allowHtml: {
        type: BooleanConstructor;
        default: boolean;
    };
    enableCodeLineNumber: {
        type: BooleanConstructor;
        default: boolean;
    };
    enableLatex: {
        type: BooleanConstructor;
        default: boolean;
    };
    enableBreaks: {
        type: BooleanConstructor;
        default: boolean;
    };
    codeXRender: {
        type: ObjectConstructor;
        default: () => {};
    };
    codeXSlot: {
        type: ObjectConstructor;
        default: () => {};
    };
    codeHighlightTheme: {
        type: PropType<import('shiki').BuiltinTheme | null>;
        default: () => null;
    };
    customAttrs: {
        type: PropType<import('../..').CustomAttrs>;
        default: () => {};
    };
    remarkPlugins: {
        type: PropType<import('unified').PluggableList>;
        default: () => never[];
    };
    remarkPluginsAhead: {
        type: PropType<import('unified').PluggableList>;
        default: () => never[];
    };
    rehypePlugins: {
        type: PropType<import('unified').PluggableList>;
        default: () => never[];
    };
    rehypePluginsAhead: {
        type: PropType<import('unified').PluggableList>;
        default: () => never[];
    };
    rehypeOptions: {
        type: PropType<Record<string, any>>;
        default: () => {};
    };
    sanitize: {
        type: BooleanConstructor;
        default: boolean;
    };
    sanitizeOptions: {
        type: PropType<import('../..').SanitizeOptions>;
        default: () => {};
    };
    mermaidConfig: {
        type: PropType<Partial<import('../Mermaid/types').MermaidToolbarConfig>>;
        default: () => {};
    };
    langs: {
        type: PropType<import('../../shared').InitShikiOptions["langs"]>;
        default: () => never[];
    };
    defaultThemeMode: {
        type: PropType<"light" | "dark">;
        default: string;
    };
    themes: {
        type: PropType<import('../../shared').InitShikiOptions["themes"]>;
        default: () => {
            [x: string]: import('shiki').ThemeRegistrationAny | import('shiki').StringLiteralUnion<import('shiki').BundledTheme, string> | undefined;
        };
    };
    colorReplacements: {
        type: PropType<import('../../shared').InitShikiOptions["colorReplacements"]>;
        default: () => {};
    };
    needViewCodeBtn: {
        type: BooleanConstructor;
        default: boolean;
    };
    secureViewCode: {
        type: BooleanConstructor;
        default: boolean;
    };
    viewCodeModalOptions: {
        type: PropType<import('../RunCode/type').ElxRunCodeOptions>;
        default: () => {};
    };
    isDark: {
        type: BooleanConstructor;
        default: boolean;
    };
    globalShiki: {
        type: PropType<GlobalShiki>;
        default: () => {};
    };
}>, () => import('vue').VNode<import('vue').RendererNode, import('vue').RendererElement, {
    [key: string]: any;
}>, {}, {}, {}, import('vue').ComponentOptionsMixin, import('vue').ComponentOptionsMixin, {}, string, import('vue').PublicProps, Readonly<import('vue').ExtractPropTypes<{
    markdown: {
        type: StringConstructor;
        default: string;
    };
    allowHtml: {
        type: BooleanConstructor;
        default: boolean;
    };
    enableCodeLineNumber: {
        type: BooleanConstructor;
        default: boolean;
    };
    enableLatex: {
        type: BooleanConstructor;
        default: boolean;
    };
    enableBreaks: {
        type: BooleanConstructor;
        default: boolean;
    };
    codeXRender: {
        type: ObjectConstructor;
        default: () => {};
    };
    codeXSlot: {
        type: ObjectConstructor;
        default: () => {};
    };
    codeHighlightTheme: {
        type: PropType<import('shiki').BuiltinTheme | null>;
        default: () => null;
    };
    customAttrs: {
        type: PropType<import('../..').CustomAttrs>;
        default: () => {};
    };
    remarkPlugins: {
        type: PropType<import('unified').PluggableList>;
        default: () => never[];
    };
    remarkPluginsAhead: {
        type: PropType<import('unified').PluggableList>;
        default: () => never[];
    };
    rehypePlugins: {
        type: PropType<import('unified').PluggableList>;
        default: () => never[];
    };
    rehypePluginsAhead: {
        type: PropType<import('unified').PluggableList>;
        default: () => never[];
    };
    rehypeOptions: {
        type: PropType<Record<string, any>>;
        default: () => {};
    };
    sanitize: {
        type: BooleanConstructor;
        default: boolean;
    };
    sanitizeOptions: {
        type: PropType<import('../..').SanitizeOptions>;
        default: () => {};
    };
    mermaidConfig: {
        type: PropType<Partial<import('../Mermaid/types').MermaidToolbarConfig>>;
        default: () => {};
    };
    langs: {
        type: PropType<import('../../shared').InitShikiOptions["langs"]>;
        default: () => never[];
    };
    defaultThemeMode: {
        type: PropType<"light" | "dark">;
        default: string;
    };
    themes: {
        type: PropType<import('../../shared').InitShikiOptions["themes"]>;
        default: () => {
            [x: string]: import('shiki').ThemeRegistrationAny | import('shiki').StringLiteralUnion<import('shiki').BundledTheme, string> | undefined;
        };
    };
    colorReplacements: {
        type: PropType<import('../../shared').InitShikiOptions["colorReplacements"]>;
        default: () => {};
    };
    needViewCodeBtn: {
        type: BooleanConstructor;
        default: boolean;
    };
    secureViewCode: {
        type: BooleanConstructor;
        default: boolean;
    };
    viewCodeModalOptions: {
        type: PropType<import('../RunCode/type').ElxRunCodeOptions>;
        default: () => {};
    };
    isDark: {
        type: BooleanConstructor;
        default: boolean;
    };
    globalShiki: {
        type: PropType<GlobalShiki>;
        default: () => {};
    };
}>> & Readonly<{}>, {
    sanitize: boolean;
    markdown: string;
    customAttrs: any;
    remarkPlugins: any;
    rehypePlugins: any;
    rehypeOptions: any;
    sanitizeOptions: any;
    themes: any;
    colorReplacements: any;
    enableCodeLineNumber: boolean;
    codeXSlot: Record<string, any>;
    globalShiki: any;
    isDark: boolean;
    viewCodeModalOptions: any;
    codeXRender: Record<string, any>;
    allowHtml: boolean;
    enableLatex: boolean;
    enableBreaks: boolean;
    rehypePluginsAhead: any;
    remarkPluginsAhead: any;
    langs: any;
    codeHighlightTheme: any;
    mermaidConfig: any;
    defaultThemeMode: any;
    needViewCodeBtn: boolean;
    secureViewCode: boolean;
}, {}, {}, {}, string, import('vue').ComponentProvideOptions, true, {}, any>;
declare function useMarkdownContext(): Ref<MarkdownContext>;
export { MarkdownProvider, useMarkdownContext };
