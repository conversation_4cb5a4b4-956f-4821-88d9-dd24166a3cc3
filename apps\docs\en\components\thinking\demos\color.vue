<docs>
---
title: Color and BackgroundColor
---

Quickly set the background color and font color of the content area through `color` and `backgroundColor`. Type is `string`, meaning you can use CSS color values.
</docs>

<template>
  <Thinking
    content="Welcome to Element-Plus-X 🍉🍉🍉"
    color="#fff"
    background-color="linear-gradient(to bottom right, rgba(190, 126, 246, 1), rgba(95, 13, 245, 1), rgba(186, 74, 227, 1))"
  />
</template>
