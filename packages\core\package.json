{"name": "vue-element-plus-x", "type": "module", "version": "1.3.4", "license": "MIT", "homepage": "https://element-plus-x.com", "repository": {"type": "git", "url": "git+https://github.com/HeJiaYue520/Element-Plus-X.git"}, "bugs": {"url": "https://github.com/HeJiaYue520/Element-Plus-X/issues", "email": "<EMAIL>"}, "keywords": ["vue", "element-plus", "element-plus-x", "vue-element-plus-x", "vue3", "vue3-element-plus", "vue3-element-plus-x", "vue3-element-plus-x-components"], "sideEffects": ["**/*.css", "**/*.scss", "**/*.vue"], "exports": {".": {"types": "./types/index.d.ts", "import": "./dist/es/index.js", "require": "./dist/umd/index.js"}, "./es/*": "./dist/es/*", "./styles/*": "./dist/styles/*", "./types/*": "./types/components/*/types.d.ts"}, "main": "dist/es/index.js", "module": "dist/es/index.js", "browser": "dist/umd/index.js", "unpkg": "dist/umd/index.js", "jsdelivr": "dist/umd/index.js", "types": "types/index.d.ts", "typesVersions": {"*": {"": ["./types/index.d.ts"], "./types/*": ["types/components/*/types.d.ts"]}}, "files": ["!dist/**/*.map", "README.md", "dist", "types/components", "types/components.d.ts", "types/dom-speech-recognition-env.d.ts", "types/hooks", "types/index.d.ts"], "scripts": {"build:es": "vue-tsc -b && vite build", "build:umd": "vue-tsc -b && vite build --config vite.config.umd.ts", "build": "rimraf dist && rimraf types && pnpm run aeac && pnpm run build:es && pnpm run build:umd", "preview": "vite preview", "dev": "storybook dev -p 6006", "build:storybook": "storybook build", "aeac": "rimraf src/index.ts && rimraf src/install.ts && esno .build/scripts/auto-export-all-components.js"}, "peerDependencies": {"vue": "^3.5.17"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@shikijs/transformers": "^3.7.0", "@vueuse/core": "^13.4.0", "chatarea": "^5.7.1", "deepmerge": "^4.3.1", "dompurify": "^3.2.6", "github-markdown-css": "^5.8.1", "highlight.js": "^11.11.1", "prismjs": "^1.30.0", "property-information": "^7.1.0", "radash": "^12.1.1", "rehype-katex": "^7.0.1", "rehype-raw": "^7.0.0", "rehype-sanitize": "^6.0.0", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "remark-parse": "^11.0.0", "remark-rehype": "^11.1.2", "rollup-plugin-visualizer": "^6.0.3", "shiki": "^3.7.0", "swrv": "^1.1.0", "ts-md5": "^2.0.1", "typescript-api-pro": "^0.0.6", "unified": "^11.0.5"}, "devDependencies": {"@antfu/eslint-config": "^4.16.1", "@chromatic-com/storybook": "^3.2.7", "@jsonlee_12138/markdown-it-mermaid": "0.0.6", "@storybook/addon-essentials": "^8.6.14", "@storybook/addon-onboarding": "^8.6.14", "@storybook/addons": "^7.6.17", "@storybook/api": "^7.6.17", "@storybook/blocks": "^8.6.14", "@storybook/experimental-addon-test": "^8.6.14", "@storybook/manager-api": "^8.6.14", "@storybook/test": "^8.6.14", "@storybook/theming": "^8.6.14", "@storybook/vue3": "^8.6.14", "@storybook/vue3-vite": "^8.6.14", "@types/dom-speech-recognition": "^0.0.4", "@types/fs-extra": "^11.0.4", "@types/markdown-it": "^14.1.2", "@types/prismjs": "^1.26.5", "@vitejs/plugin-vue": "^5.2.4", "@vitest/browser": "^3.2.4", "@vitest/coverage-v8": "^3.2.4", "@vue/tsconfig": "^0.7.0", "element-plus": "^2.9.7", "esno": "^4.8.0", "fast-glob": "^3.3.3", "playwright": "^1.53.2", "rimraf": "^6.0.1", "sass": "^1.89.2", "storybook": "^8.6.14", "storybook-dark-mode": "^4.0.2", "terser": "^5.43.1", "typescript": "~5.8.3", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.8.0", "vite": "^6.3.5", "vite-plugin-dts": "^4.5.4", "vite-plugin-lib-inject-css": "^2.2.2", "vitest": "^3.2.4", "vue-tsc": "^2.2.10"}}