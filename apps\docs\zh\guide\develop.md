#### **一、克隆仓库**

```bash
git clone https://github.com/HeJiaYue520/Element-Plus-X.git
cd Element-Plus-X
```

#### **二、安装依赖**

**推荐使用 PNPM 管理依赖**

```bash
pnpm install
```

#### **三、项目结构**

```plaintext
   ├── apps\docs              # 文档
   |     └── components       # 组件库文档
   └── packages\core          # 核心代码
         └── src
            ├── components    # 组件源码
            └── stories       # 组件演示用例

```

#### **四、开发命令**

**请先构建好组件在执行预览**

| 命令            | 说明                           |
| --------------- | ------------------------------ |
| `pnpm build`    | 在本地构建生产版本             |
| `pnpm dev:core` | 启动组件测试使用 storybook预览 |
| `pnpm dev:docs` | 本地预览文档                   |

#### **五、成为贡献者**

> 🥰 在提交 pr 之前，我们非常希望，也非常欢迎，你能加入我们的交流群。
>
> 😔 为了避免浪费你宝贵的时间。请先在交流群中沟通和讨论，和我们团队项目负责人取得联系，一起交流，确定好想做的功能，明确好要做的需求。
>
> 💌 提交 pr 后我们可以相互交流，互相学习。集思广益，改进 pr 方案，共同进步。期待你的加入

1. **创建一个自己的分支**：

   分支管理尤为重要，我们决定将 main 分支，作为上线的主分支，我们的 pr 提交，全部合并到 dev 开发分支。在创建 pr 后，我们会将先检查，哪些是较为有用的 pr ，当我们认可了你的提交后，我们会为你创建新的分支，将你的代码合并到该分支中，同时我们会将代码拉取，本地运行审核。如果有需要改进的地方，我们会及时向您反馈。如果我们审核没有通过，也会给出相关的建议，请放心大胆的提交的想法和创意。

   ```bash
   git checkout -b feature/new-component (new-component 就是你的分支名)
   ```

2. **代码规范**：

   请在执行完开发命令后，安装并打开 **Vs Code 的 `ESLint` 插件** 使用 `ESLint.9x` 格式化代码（保存代码时自动检查）
   - 组件命名遵循 `PascalCase` 规范

   - 每个组件包含：

   ```plaintext
   ├── components      # 组件涉及到的子组件 （可选）
   ├── index.vue       # 组件实现
   ├── types.d.ts      # 类型定义
   └── style.scss      # 样式文件
   ```

3. **提交 PR**：
   - 标题格式：`feat(component): 新增打字机组件`
   - 描述包含：功能说明、使用示例、变更影响

4. **研究审核**：

   🙋 再次提醒：我们会将先检查，哪些是有效的 pr ，当我们认可了你的提交后，我们会为你创建新的分支，将你的代码合并到该分支中，同时我们会将代码拉取，本地运行审核。如果有需要改进的地方，我们会及时向您反馈。如果我们审核没有通过，也会给出相关的建议，请放心大胆的提交的想法和创意。

#### **六、调试本地包**

在示例项目中链接本地代码：

```bash
# 先本地构建
pnpm build

# 示例项目执行
pnpm run dev:core
```

#### 七、**常见问题**

1. **样式冲突**：
   - 确保没有重复引入 Element-Plus 样式
   - 使用 `deep()` 选择器覆盖组件样式
2. **类型错误**：
   - 暂无
3. **语音功能异常**：
   - 检查浏览器权限设置

4. **版本问题**：

   Vue 版本过低导致，请升级 Vue3.3+ 或最新版本

   pnpm 版本过低，请升级 10+ 或最新版本
