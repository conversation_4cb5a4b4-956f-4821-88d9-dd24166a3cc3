.conversations-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
  width: fit-content;
  box-sizing: border-box;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.conversations-list {
  list-style: none;
  margin: 0;
  padding: 0;
  flex: 1;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;

  // 默认样式
  &.conversations-list--default {
    padding: 10px 0 10px 10px;
    background-color: #fff;
    border-radius: 8px;
    width: 280px;
    height: 0;
  }
}

.conversations-scroll-wrapper {
  flex: 1;
  overflow: hidden;
  position: relative;

  /* 在右侧添加留白区域 */
  &::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 8px;
    /* 右侧留白宽度 */
    height: 100%;
    background-color: transparent;
    pointer-events: none;
    /* 确保不影响交互 */
  }
}

// 加载更多
.conversations-load-more {
  display: flex;
  width: calc(100% - 20px);
  padding: 4px 0;
  justify-content: center;
  align-items: center;
  font-size: 12px;
  gap: 3px;
  height: 100%;
  color: #909399;
  background-color: var(--conversation-list-auto-bg-color, #fff);
}

// 加载动画
.conversations-load-more-is-loading {
  margin-top: 2px;
  animation: spinloading 2s linear infinite;
}

@keyframes spinloading {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.scroll-content {
  min-height: 100%;
}

.loading-more {
  text-align: center;
  padding: 10px 0;
  color: #909399;
  font-size: 14px;
}

.conversation-group {
  position: relative;

  &:last-child {
    margin-bottom: 0;
  }

  .conversation-group-title {
    font-size: 14px;
    color: #909399;
    padding: 8px 0;
    font-weight: 500;
    margin-bottom: 4px;
    border-radius: 4px;
    border-top-left-radius: 0px;
    border-top-right-radius: 0px;
    // 预留滚动条宽度
    width: calc(100% - 10px);
    box-sizing: border-box;
  }

  .sticky-title {
    position: sticky;
    top: 0;
    z-index: 20;
    background-color: var(--conversation-list-auto-bg-color, #fff);
  }

  .active-sticky {
    z-index: 10;
  }

  .conversation-group-items {
    padding-top: 0;
  }
}

.scroll-to-top-btn {
  position: absolute;
  right: 16px;
  bottom: 16px;
  z-index: 99;
  opacity: 0.8;
  transition: opacity 0.3s;

  &:hover {
    opacity: 1;
  }
}
